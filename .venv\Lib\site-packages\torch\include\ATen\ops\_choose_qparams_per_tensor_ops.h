#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API _choose_qparams_per_tensor {
  using schema = ::std::tuple<double,int64_t> (const at::Tensor &, bool);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_choose_qparams_per_tensor";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "_choose_qparams_per_tensor(Tensor self, bool reduce_range=False) -> (float, int)";
  static ::std::tuple<double,int64_t> call(const at::Tensor & self, bool reduce_range);
  static ::std::tuple<double,int64_t> redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, bool reduce_range);
};

}} // namespace at::_ops

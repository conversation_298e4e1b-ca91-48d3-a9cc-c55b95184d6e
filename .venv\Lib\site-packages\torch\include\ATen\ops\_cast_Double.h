#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/_cast_Double_ops.h>

namespace at {


// aten::_cast_Double(Tensor self, bool non_blocking=False) -> Tensor
inline at::Tensor _cast_Double(const at::Tensor & self, bool non_blocking=false) {
    return at::_ops::_cast_Double::call(self, non_blocking);
}

}

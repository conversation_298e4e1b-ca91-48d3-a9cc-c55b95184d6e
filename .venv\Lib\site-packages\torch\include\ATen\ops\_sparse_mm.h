#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/_sparse_mm_ops.h>

namespace at {


// aten::_sparse_mm(Tensor sparse, Tensor dense) -> Tensor
inline at::Tensor _sparse_mm(const at::Tensor & sparse, const at::Tensor & dense) {
    return at::_ops::_sparse_mm::call(sparse, dense);
}

// aten::_sparse_mm.reduce(Tensor sparse, Tensor dense, str reduce) -> Tensor
inline at::Tensor _sparse_mm(const at::Tensor & sparse, const at::Tensor & dense, c10::string_view reduce) {
    return at::_ops::_sparse_mm_reduce::call(sparse, dense, reduce);
}

}

#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>


namespace at {
namespace native {
TORCH_API void cummax_helper_cpu(const at::Tensor & self, at::Tensor & values, at::Tensor & indices, int64_t dim);
TORCH_API void cummax_helper_cuda(const at::Tensor & self, at::Tensor & values, at::Tensor & indices, int64_t dim);
} // namespace native
} // namespace at

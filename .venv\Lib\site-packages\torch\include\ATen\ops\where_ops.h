#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API where_self {
  using schema = at::Tensor (const at::Tensor &, const at::Tensor &, const at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::where";
  static constexpr const char* overload_name = "self";
  static constexpr const char* schema_str = "where.self(Tensor condition, Tensor self, Tensor other) -> Tensor";
  static at::Tensor call(const at::Tensor & condition, const at::Tensor & self, const at::Tensor & other);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & condition, const at::Tensor & self, const at::Tensor & other);
};

struct TORCH_API where_self_out {
  using schema = at::Tensor & (const at::Tensor &, const at::Tensor &, const at::Tensor &, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::where";
  static constexpr const char* overload_name = "self_out";
  static constexpr const char* schema_str = "where.self_out(Tensor condition, Tensor self, Tensor other, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & condition, const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & condition, const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
};

struct TORCH_API where_ScalarSelf {
  using schema = at::Tensor (const at::Tensor &, const at::Scalar &, const at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::where";
  static constexpr const char* overload_name = "ScalarSelf";
  static constexpr const char* schema_str = "where.ScalarSelf(Tensor condition, Scalar self, Tensor other) -> Tensor";
  static at::Tensor call(const at::Tensor & condition, const at::Scalar & self, const at::Tensor & other);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & condition, const at::Scalar & self, const at::Tensor & other);
};

struct TORCH_API where_ScalarOther {
  using schema = at::Tensor (const at::Tensor &, const at::Tensor &, const at::Scalar &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::where";
  static constexpr const char* overload_name = "ScalarOther";
  static constexpr const char* schema_str = "where.ScalarOther(Tensor condition, Tensor self, Scalar other) -> Tensor";
  static at::Tensor call(const at::Tensor & condition, const at::Tensor & self, const at::Scalar & other);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & condition, const at::Tensor & self, const at::Scalar & other);
};

struct TORCH_API where_Scalar {
  using schema = at::Tensor (const at::Tensor &, const at::Scalar &, const at::Scalar &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::where";
  static constexpr const char* overload_name = "Scalar";
  static constexpr const char* schema_str = "where.Scalar(Tensor condition, Scalar self, Scalar other) -> Tensor";
  static at::Tensor call(const at::Tensor & condition, const at::Scalar & self, const at::Scalar & other);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & condition, const at::Scalar & self, const at::Scalar & other);
};

struct TORCH_API where {
  using schema = ::std::vector<at::Tensor> (const at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::where";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "where(Tensor condition) -> Tensor[]";
  static ::std::vector<at::Tensor> call(const at::Tensor & condition);
  static ::std::vector<at::Tensor> redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & condition);
};

}} // namespace at::_ops

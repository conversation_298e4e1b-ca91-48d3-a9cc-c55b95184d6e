#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/_spsolve_ops.h>

namespace at {


// aten::_spsolve(Tensor A, Tensor B, *, bool left=True) -> Tensor
inline at::Tensor _spsolve(const at::Tensor & A, const at::Tensor & B, bool left=true) {
    return at::_ops::_spsolve::call(A, B, left);
}

}

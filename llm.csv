input_query,output_config
simulate water flow in a pipe at 2 m/s,geometry: pipe; fluid: water; velocity: 2; solver: simpleFoam
simulate laminar flow over a flat plate,geometry: plate; fluid: water; flow: laminar; solver: simpleFoam
flow of water in a square duct at 5 m/s,geometry: duct; shape: square; fluid: water; velocity: 5; solver: simpleFoam
simulate 2D water flow over cylinder,geometry: cylinder; fluid: water; type: 2D; solver: icoFoam
simulate water velocity in a narrow channel,geometry: channel; fluid: water; velocity: unknown; solver: simpleFoam
flow around a sphere with 1.5 m/s,geometry: sphere; fluid: water; velocity: 1.5; solver: simpleFoam
run turbulent water simulation over wing,geometry: wing; fluid: water; flow: turbulent; solver: simpleFoam
simulate flow past airfoil with water,geometry: airfoil; fluid: water; flow: steady; solver: simpleFoam
steady flow of water in pipe at 4 m/s,geometry: pipe; fluid: water; velocity: 4; flow: steady; solver: simpleFoam
run simulation of water over boat hull,geometry: boat; fluid: water; region: hull; solver: interFoam
simulate free surface flow in tank,geometry: tank; fluid: water; type: freeSurface; solver: interFoam
model wave motion in open channel,geometry: channel; fluid: water; motion: wave; solver: interFoam
run buoyant water flow test,geometry: tank; fluid: water; property: buoyant; solver: buoyantSimpleFoam
model vertical pipe flow of water at 3 m/s,geometry: pipe; orientation: vertical; fluid: water; velocity: 3; solver: simpleFoam
simulate water filling a cube tank,geometry: tank; fluid: water; shape: cube; solver: interFoam
simulate drainage in inclined pipe,geometry: pipe; fluid: water; slope: inclined; solver: simpleFoam
turbulent flow in horizontal pipe at 6 m/s,geometry: pipe; fluid: water; orientation: horizontal; velocity: 6; flow: turbulent; solver: simpleFoam
simulate vortex formation in container,geometry: container; fluid: water; effect: vortex; solver: interFoam
simulate 3 m/s inlet water flow in duct,geometry: duct; fluid: water; inletVelocity: 3; solver: simpleFoam
simulate mixing of water streams in Y-junction,geometry: Y-junction; fluid: water; mixing: true; solver: interFoam
simulate stratified water flow in pipe,geometry: pipe; fluid: water; flow: stratified; solver: interFoam
simulate water over dam break scenario,geometry: dam; fluid: water; scenario: damBreak; solver: interFoam
simulate 2D channel flow of water,geometry: channel; fluid: water; type: 2D; solver: icoFoam
simulate compressible flow of water,geometry: pipe; fluid: water; flow: compressible; solver: rhoPimpleFoam
simulate water jet entering tank,geometry: tank; fluid: water; inlet: jet; solver: interFoam
simulate confined water flow in square box,geometry: box; fluid: water; confinement: true; shape: square; solver: interFoam
simulate flow separation behind cylinder,geometry: cylinder; fluid: water; effect: separation; solver: simpleFoam
run swirl water simulation in pipe,geometry: pipe; fluid: water; swirl: true; solver: interFoam
simulate pressure drop in pipe with 4 m/s,geometry: pipe; fluid: water; velocity: 4; calc: pressureDrop; solver: simpleFoam
simulate heat transfer in water pipe,geometry: pipe; fluid: water; physics: heatTransfer; solver: buoyantSimpleFoam
run compressible turbulent water flow,geometry: duct; fluid: water; flow: turbulent; compressible: true; solver: rhoPimpleFoam
simulate jet impact on surface with 3.5 m/s,geometry: plate; fluid: water; jetVelocity: 3.5; solver: interFoam
simulate transient water flow in elbow pipe,geometry: elbow; fluid: water; flow: transient; solver: pisoFoam
flow of water past rectangular prism,geometry: prism; shape: rectangular; fluid: water; solver: simpleFoam
simulate water leakage from pipe hole,geometry: pipe; fluid: water; fault: leakage; solver: interFoam
simulate vertical tank draining,geometry: tank; orientation: vertical; fluid: water; process: draining; solver: interFoam
simulate flow over trapezoidal ramp,geometry: ramp; shape: trapezoidal; fluid: water; solver: simpleFoam
simulate jet mixing in square tank,geometry: tank; fluid: water; shape: square; mixing: jet; solver: interFoam
simulate 2 m/s turbulent jet over wall,geometry: wall; fluid: water; jet: true; velocity: 2; flow: turbulent; solver: interFoam
simulate oscillating flow in horizontal pipe,geometry: pipe; fluid: water; motion: oscillating; orientation: horizontal; solver: interFoam
simulate backflow in water channel,geometry: channel; fluid: water; condition: backflow; solver: interFoam
simulate rotating water in cylinder,geometry: cylinder; fluid: water; motion: rotating; solver: interFoam
simulate evaporation in water pool,geometry: pool; fluid: water; phenomenon: evaporation; solver: interFoam
simulate flooding in square basin,geometry: basin; fluid: water; event: flood; shape: square; solver: interFoam
simulate pipe burst water release,geometry: pipe; fluid: water; failure: burst; solver: interFoam
simulate water splash on wall,geometry: wall; fluid: water; effect: splash; solver: interFoam
simulate fluid interaction with valve,geometry: valve; fluid: water; solver: interFoam
simulate multi-phase flow in pipe,geometry: pipe; fluid: water; phase: multi; solver: multiphaseEulerFoam
simulate water flow around pillar,geometry: pillar; fluid: water; solver: simpleFoam
simulate pressure pulse in closed tank,geometry: tank; fluid: water; condition: pressurePulse; solver: interFoam
simulate submerged flow over step,geometry: step; fluid: water; condition: submerged; solver: interFoam

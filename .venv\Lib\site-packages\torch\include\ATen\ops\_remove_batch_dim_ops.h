#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API _remove_batch_dim {
  using schema = at::Tensor (const at::Tensor &, int64_t, c10::SymInt, int64_t);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_remove_batch_dim";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "_remove_batch_dim(Tensor self, int level, SymInt batch_size, int out_dim) -> Tensor";
  static at::Tensor call(const at::Tensor & self, int64_t level, c10::SymInt batch_size, int64_t out_dim);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, int64_t level, c10::SymInt batch_size, int64_t out_dim);
};

}} // namespace at::_ops

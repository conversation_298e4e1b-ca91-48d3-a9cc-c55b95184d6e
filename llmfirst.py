import pandas as pd
import numpy as np
import tensorflow as tf
from tensorflow.keras.preprocessing.text import Tokenizer
from tensorflow.keras.preprocessing.sequence import pad_sequences
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Embedding, LSTM, Dense
from sklearn.model_selection import train_test_split

# STEP 1: Load Data
df = pd.read_csv("llm.csv")

input_texts = df["input_query"].astype(str).tolist()
target_texts = df["output_config"].astype(str).tolist()

# STEP 2: Tokenization
MAX_NUM_WORDS = 1000
MAX_SEQ_LEN = 30

input_tokenizer = Tokenizer(num_words=MAX_NUM_WORDS, oov_token="<OOV>")
input_tokenizer.fit_on_texts(input_texts)
input_sequences = input_tokenizer.texts_to_sequences(input_texts)
input_padded = pad_sequences(input_sequences, maxlen=MAX_SEQ_LEN, padding='post')

output_tokenizer = Tokenizer(num_words=MAX_NUM_WORDS, oov_token="<OOV>")
output_tokenizer.fit_on_texts(target_texts)
output_sequences = output_tokenizer.texts_to_sequences(target_texts)
output_padded = pad_sequences(output_sequences, maxlen=MAX_SEQ_LEN, padding='post')

# STEP 3: Train-test split
X_train, X_test, y_train, y_test = train_test_split(input_padded, output_padded, test_size=0.2)

# STEP 4: Build the model
model = Sequential([
    Embedding(input_dim=MAX_NUM_WORDS, output_dim=64, input_length=MAX_SEQ_LEN),
    LSTM(64, return_sequences=False),
    Dense(MAX_SEQ_LEN * len(output_tokenizer.word_index) // 10, activation='relu'),
    Dense(MAX_SEQ_LEN, activation='softmax')  # Predicts index positions
])

model.compile(loss='sparse_categorical_crossentropy', optimizer='adam', metrics=['accuracy'])

# STEP 5: Train
print("\n📦 Training model...\n")
model.fit(X_train, np.array(y_train), epochs=30, batch_size=16, validation_data=(X_test, np.array(y_test)))

# STEP 6: User input testing
print("\n💬 Test the model:")
while True:
    user_input = input("\nEnter natural language OpenFOAM query (or 'exit'): ")
    if user_input.strip().lower() == 'exit':
        break
    seq = input_tokenizer.texts_to_sequences([user_input])
    pad = pad_sequences(seq, maxlen=MAX_SEQ_LEN, padding='post')
    pred = model.predict(pad)
    pred_ids = np.argmax(pred, axis=1)
    pred_tokens = [list(output_tokenizer.word_index.keys())[list(output_tokenizer.word_index.values()).index(id_)] if id_ in output_tokenizer.word_index.values() else '?' for id_ in pred_ids]
    print("🔧 Predicted config:", " ".join(pred_tokens))
